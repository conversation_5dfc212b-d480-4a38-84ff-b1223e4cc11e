[{"D:\\ASL\\training-frontend\\src\\index.js": "1", "D:\\ASL\\training-frontend\\src\\App.js": "2", "D:\\ASL\\training-frontend\\src\\reportWebVitals.js": "3", "D:\\ASL\\training-frontend\\src\\components\\HomePage.js": "4", "D:\\ASL\\training-frontend\\src\\components\\TrainingPage.js": "5", "D:\\ASL\\training-frontend\\src\\components\\AboutPage.js": "6", "D:\\ASL\\training-frontend\\src\\components\\ContactPage.js": "7", "D:\\ASL\\training-frontend\\src\\hooks\\useSignDetection.js": "8"}, {"size": 535, "mtime": 1751014435070, "results": "9", "hashOfConfig": "10"}, {"size": 2014, "mtime": 1751019716269, "results": "11", "hashOfConfig": "10"}, {"size": 362, "mtime": 1751014435142, "results": "12", "hashOfConfig": "10"}, {"size": 16625, "mtime": 1751019716175, "results": "13", "hashOfConfig": "10"}, {"size": 42221, "mtime": 1751113761087, "results": "14", "hashOfConfig": "10"}, {"size": 6851, "mtime": 1751019090768, "results": "15", "hashOfConfig": "10"}, {"size": 10870, "mtime": 1751019063718, "results": "16", "hashOfConfig": "10"}, {"size": 7122, "mtime": 1751119053043, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e98u2r", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\ASL\\training-frontend\\src\\index.js", [], [], "D:\\ASL\\training-frontend\\src\\App.js", [], [], "D:\\ASL\\training-frontend\\src\\reportWebVitals.js", [], [], "D:\\ASL\\training-frontend\\src\\components\\HomePage.js", ["42", "43", "44", "45"], [], "D:\\ASL\\training-frontend\\src\\components\\TrainingPage.js", [], ["46"], "D:\\ASL\\training-frontend\\src\\components\\AboutPage.js", ["47", "48", "49", "50"], [], "D:\\ASL\\training-frontend\\src\\components\\ContactPage.js", [], [], "D:\\ASL\\training-frontend\\src\\hooks\\useSignDetection.js", ["51"], [], {"ruleId": "52", "severity": 1, "message": "53", "line": 6, "column": 3, "nodeType": "54", "messageId": "55", "endLine": 6, "endColumn": 8}, {"ruleId": "52", "severity": 1, "message": "56", "line": 14, "column": 3, "nodeType": "54", "messageId": "55", "endLine": 14, "endColumn": 6}, {"ruleId": "52", "severity": 1, "message": "57", "line": 15, "column": 3, "nodeType": "54", "messageId": "55", "endLine": 15, "endColumn": 8}, {"ruleId": "52", "severity": 1, "message": "58", "line": 16, "column": 3, "nodeType": "54", "messageId": "55", "endLine": 16, "endColumn": 13}, {"ruleId": "52", "severity": 1, "message": "59", "line": 1097, "column": 26, "nodeType": "54", "messageId": "55", "endLine": 1097, "endColumn": 43, "suppressions": "60"}, {"ruleId": "52", "severity": 1, "message": "56", "line": 10, "column": 3, "nodeType": "54", "messageId": "55", "endLine": 10, "endColumn": 6}, {"ruleId": "52", "severity": 1, "message": "61", "line": 11, "column": 3, "nodeType": "54", "messageId": "55", "endLine": 11, "endColumn": 8}, {"ruleId": "52", "severity": 1, "message": "62", "line": 12, "column": 3, "nodeType": "54", "messageId": "55", "endLine": 12, "endColumn": 6}, {"ruleId": "52", "severity": 1, "message": "63", "line": 224, "column": 7, "nodeType": "54", "messageId": "55", "endLine": 224, "endColumn": 19}, {"ruleId": "64", "severity": 1, "message": "65", "line": 115, "column": 6, "nodeType": "66", "endLine": 115, "endColumn": 8, "suggestions": "67"}, "no-unused-vars", "'Users' is defined but never used.", "Identifier", "unusedVar", "'Eye' is defined but never used.", "'Award' is defined but never used.", "'TrendingUp' is defined but never used.", "'setRecordedVideos' is assigned a value but never used.", ["68"], "'Globe' is defined but never used.", "'Zap' is defined but never used.", "'SectionTitle' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'lastPrediction'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setPrediction' needs the current value of 'lastPrediction'.", "ArrayExpression", ["69"], {"kind": "70", "justification": "71"}, {"desc": "72", "fix": "73"}, "directive", "", "Update the dependencies array to be: [lastPrediction]", {"range": "74", "text": "75"}, [4513, 4515], "[lastPrediction]"]