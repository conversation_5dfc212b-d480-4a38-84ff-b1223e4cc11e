[{"D:\\ASL\\training-frontend\\src\\index.js": "1", "D:\\ASL\\training-frontend\\src\\App.js": "2", "D:\\ASL\\training-frontend\\src\\reportWebVitals.js": "3", "D:\\ASL\\training-frontend\\src\\components\\HomePage.js": "4", "D:\\ASL\\training-frontend\\src\\components\\TrainingPage.js": "5", "D:\\ASL\\training-frontend\\src\\components\\AboutPage.js": "6", "D:\\ASL\\training-frontend\\src\\components\\ContactPage.js": "7", "D:\\ASL\\training-frontend\\src\\hooks\\useSignDetection.js": "8"}, {"size": 535, "mtime": 1751014435070, "results": "9", "hashOfConfig": "10"}, {"size": 2014, "mtime": 1751019716269, "results": "11", "hashOfConfig": "10"}, {"size": 362, "mtime": 1751014435142, "results": "12", "hashOfConfig": "10"}, {"size": 16625, "mtime": 1751019716175, "results": "13", "hashOfConfig": "10"}, {"size": 43807, "mtime": 1751144231956, "results": "14", "hashOfConfig": "10"}, {"size": 6851, "mtime": 1751019090768, "results": "15", "hashOfConfig": "10"}, {"size": 10870, "mtime": 1751019063718, "results": "16", "hashOfConfig": "10"}, {"size": 7122, "mtime": 1751119053043, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e98u2r", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\ASL\\training-frontend\\src\\index.js", [], [], "D:\\ASL\\training-frontend\\src\\App.js", [], [], "D:\\ASL\\training-frontend\\src\\reportWebVitals.js", [], [], "D:\\ASL\\training-frontend\\src\\components\\HomePage.js", ["42", "43", "44", "45"], [], "D:\\ASL\\training-frontend\\src\\components\\TrainingPage.js", ["46", "47", "48", "49", "50", "51", "52"], ["53"], "D:\\ASL\\training-frontend\\src\\components\\AboutPage.js", ["54", "55", "56", "57"], [], "D:\\ASL\\training-frontend\\src\\components\\ContactPage.js", [], [], "D:\\ASL\\training-frontend\\src\\hooks\\useSignDetection.js", ["58"], [], {"ruleId": "59", "severity": 1, "message": "60", "line": 6, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 6, "endColumn": 8}, {"ruleId": "59", "severity": 1, "message": "63", "line": 14, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 14, "endColumn": 6}, {"ruleId": "59", "severity": 1, "message": "64", "line": 15, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 15, "endColumn": 8}, {"ruleId": "59", "severity": 1, "message": "65", "line": 16, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 16, "endColumn": 13}, {"ruleId": "59", "severity": 1, "message": "66", "line": 16, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 16, "endColumn": 9}, {"ruleId": "59", "severity": 1, "message": "67", "line": 17, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 17, "endColumn": 4}, {"ruleId": "59", "severity": 1, "message": "68", "line": 364, "column": 7, "nodeType": "61", "messageId": "62", "endLine": 364, "endColumn": 22}, {"ruleId": "59", "severity": 1, "message": "69", "line": 369, "column": 7, "nodeType": "61", "messageId": "62", "endLine": 369, "endColumn": 18}, {"ruleId": "59", "severity": 1, "message": "70", "line": 396, "column": 7, "nodeType": "61", "messageId": "62", "endLine": 396, "endColumn": 17}, {"ruleId": "59", "severity": 1, "message": "71", "line": 405, "column": 7, "nodeType": "61", "messageId": "62", "endLine": 405, "endColumn": 18}, {"ruleId": "59", "severity": 1, "message": "72", "line": 424, "column": 7, "nodeType": "61", "messageId": "62", "endLine": 424, "endColumn": 25}, {"ruleId": "59", "severity": 1, "message": "73", "line": 1166, "column": 26, "nodeType": "61", "messageId": "62", "endLine": 1166, "endColumn": 43, "suppressions": "74"}, {"ruleId": "59", "severity": 1, "message": "63", "line": 10, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 10, "endColumn": 6}, {"ruleId": "59", "severity": 1, "message": "75", "line": 11, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 11, "endColumn": 8}, {"ruleId": "59", "severity": 1, "message": "76", "line": 12, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 12, "endColumn": 6}, {"ruleId": "59", "severity": 1, "message": "77", "line": 224, "column": 7, "nodeType": "61", "messageId": "62", "endLine": 224, "endColumn": 19}, {"ruleId": "78", "severity": 1, "message": "79", "line": 115, "column": 6, "nodeType": "80", "endLine": 115, "endColumn": 8, "suggestions": "81"}, "no-unused-vars", "'Users' is defined but never used.", "Identifier", "unusedVar", "'Eye' is defined but never used.", "'Award' is defined but never used.", "'TrendingUp' is defined but never used.", "'Search' is defined but never used.", "'X' is defined but never used.", "'SearchContainer' is assigned a value but never used.", "'SearchInput' is assigned a value but never used.", "'SearchIcon' is assigned a value but never used.", "'ClearButton' is assigned a value but never used.", "'SearchResultsCount' is assigned a value but never used.", "'setRecordedVideos' is assigned a value but never used.", ["82"], "'Globe' is defined but never used.", "'Zap' is defined but never used.", "'SectionTitle' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'lastPrediction'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setPrediction' needs the current value of 'lastPrediction'.", "ArrayExpression", ["83"], {"kind": "84", "justification": "85"}, {"desc": "86", "fix": "87"}, "directive", "", "Update the dependencies array to be: [lastPrediction]", {"range": "88", "text": "89"}, [4513, 4515], "[lastPrediction]"]