#!/usr/bin/env python3
"""
Test script to verify landmark recording functionality
"""
import asyncio
import json
import websockets
import base64
import numpy as np
import cv2
import pandas as pd
import os

async def test_landmark_recording():
    """Test the landmark recording functionality"""
    uri = "ws://localhost:8001/ws/detect"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to backend")
            
            # Start recording for a test sign
            start_msg = {
                "type": "start_recording",
                "target_sign": "hello"
            }
            await websocket.send(json.dumps(start_msg))
            print("📤 Sent start recording message")
            
            # Wait for response
            response = await websocket.recv()
            data = json.loads(response)
            print(f"📥 Received: {data['type']}")
            
            if data['type'] == 'recording_started':
                print(f"🎬 Recording started for: {data['target_sign']}")
                print(f"📁 File will be saved to: {data['filepath']}")
                
                # Send multiple test frames to simulate a video sequence
                for frame_num in range(50):  # Send 50 frames (about 5 seconds at 10fps)
                    # Create a dummy image with some variation
                    test_image = np.zeros((480, 640, 3), dtype=np.uint8)
                    # Add some variation to make each frame slightly different
                    test_image[:] = (128 + frame_num % 50, 128, 128)  # Varying red channel
                    
                    # Add some simple "hand" simulation - a white rectangle that moves
                    hand_x = 200 + (frame_num * 5) % 200  # Moving hand
                    hand_y = 200 + (frame_num * 3) % 100
                    cv2.rectangle(test_image, (hand_x, hand_y), (hand_x + 50, hand_y + 50), (255, 255, 255), -1)
                    
                    # Encode to base64
                    _, buffer = cv2.imencode('.jpg', test_image)
                    image_b64 = base64.b64encode(buffer).decode('utf-8')
                    
                    frame_msg = {
                        "type": "frame",
                        "frame": f"data:image/jpeg;base64,{image_b64}"
                    }
                    
                    await websocket.send(json.dumps(frame_msg))
                    
                    # Wait for response
                    frame_response = await websocket.recv()
                    frame_data = json.loads(frame_response)
                    
                    if frame_num == 0:
                        print(f"📸 First frame processed: {frame_data.get('type')}")
                    elif frame_num % 10 == 0:
                        print(f"📸 Frame {frame_num} processed")
                    
                    # Small delay to simulate real video
                    await asyncio.sleep(0.1)
                
                print("📤 Sent all test frames")
                
                # Stop recording
                stop_msg = {
                    "type": "stop_recording"
                }
                await websocket.send(json.dumps(stop_msg))
                print("📤 Sent stop recording message")
                
                # Wait for stop response
                stop_response = await websocket.recv()
                stop_data = json.loads(stop_response)
                print(f"📥 Stop response: {stop_data['type']}")
                
                if stop_data['type'] == 'recording_stopped' and stop_data.get('result'):
                    result = stop_data['result']
                    print(f"✅ Recording completed!")
                    print(f"   📁 File: {result['filepath']}")
                    print(f"   🎬 Frames: {result['frame_count']}")
                    print(f"   ⏱️  Duration: {result['duration']:.1f}s")
                    print(f"   ✅ Valid: {result['is_valid']}")
                    
                    # Check if the parquet file was created
                    if os.path.exists(result['filepath']):
                        print(f"✅ Parquet file exists: {result['filepath']}")
                        
                        # Try to read the parquet file
                        try:
                            df = pd.read_parquet(result['filepath'])
                            print(f"📊 Parquet file contents:")
                            print(f"   📏 Shape: {df.shape}")
                            print(f"   📋 Columns: {list(df.columns)}")
                            print(f"   🎯 Unique frames: {df['frame'].nunique()}")
                            print(f"   🎯 Unique landmarks: {df['landmark_index'].nunique()}")
                            print(f"   📊 Sample data:")
                            print(df.head())
                            
                            # Verify the data structure matches what the model expects
                            expected_frames = result['frame_count']
                            expected_landmarks = 543
                            expected_rows = expected_frames * expected_landmarks
                            
                            if df.shape[0] == expected_rows:
                                print(f"✅ Data structure is correct: {expected_rows} rows")
                            else:
                                print(f"❌ Data structure mismatch: expected {expected_rows}, got {df.shape[0]}")
                                
                        except Exception as e:
                            print(f"❌ Error reading parquet file: {e}")
                    else:
                        print(f"❌ Parquet file not found: {result['filepath']}")
                
            else:
                print(f"❌ Unexpected response: {data}")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    print("🧪 Testing landmark recording functionality...")
    print("⚠️  Make sure the backend is running on port 8001")
    asyncio.run(test_landmark_recording())
