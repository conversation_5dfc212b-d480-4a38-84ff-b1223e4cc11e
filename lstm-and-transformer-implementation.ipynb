{"cells": [{"cell_type": "markdown", "id": "510eab72", "metadata": {"papermill": {"duration": 0.009158, "end_time": "2023-04-22T13:59:20.654814", "exception": false, "start_time": "2023-04-22T13:59:20.645656", "status": "completed"}, "tags": []}, "source": ["# GISLR notebook\n", "working notebook"]}, {"cell_type": "code", "execution_count": 1, "id": "3629d857", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:20.672926Z", "iopub.status.busy": "2023-04-22T13:59:20.671738Z", "iopub.status.idle": "2023-04-22T13:59:20.683937Z", "shell.execute_reply": "2023-04-22T13:59:20.682963Z"}, "papermill": {"duration": 0.023765, "end_time": "2023-04-22T13:59:20.686375", "exception": false, "start_time": "2023-04-22T13:59:20.662610", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import numpy as np # linear algebra\n", "import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "0cf9dfe2", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:20.702463Z", "iopub.status.busy": "2023-04-22T13:59:20.702160Z", "iopub.status.idle": "2023-04-22T13:59:30.474224Z", "shell.execute_reply": "2023-04-22T13:59:30.472916Z"}, "papermill": {"duration": 9.783552, "end_time": "2023-04-22T13:59:30.477104", "exception": false, "start_time": "2023-04-22T13:59:20.693552", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: tensorflow\r\n", "Version: 2.11.0\r\n", "Summary: TensorFlow is an open source machine learning framework for everyone.\r\n", "Home-page: https://www.tensorflow.org/\r\n", "Author: Google Inc.\r\n", "Author-email: <EMAIL>\r\n", "License: Apache 2.0\r\n", "Location: /opt/conda/lib/python3.7/site-packages\r\n", "Requires: absl-py, astunparse, flatbuffers, gast, google-pasta, grpcio, h5py, keras, libclang, numpy, opt-einsum, packaging, protobuf, setuptools, six, tensorboard, tensorflow-estimator, tensorflow-io-gcs-filesystem, termcolor, typing-extensions, wrapt\r\n", "Required-by: explainable-ai-sdk, tensorflow-cloud, tensorflow-decision-forests, tensorflow-serving-api, tensorflow-text, tensorflow-transform, tensorflowjs, tfx-bsl, witwidget\r\n"]}], "source": ["!pip show tensorflow"]}, {"cell_type": "code", "execution_count": 3, "id": "f938651b", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:30.494446Z", "iopub.status.busy": "2023-04-22T13:59:30.493394Z", "iopub.status.idle": "2023-04-22T13:59:41.782041Z", "shell.execute_reply": "2023-04-22T13:59:41.780836Z"}, "papermill": {"duration": 11.299922, "end_time": "2023-04-22T13:59:41.784640", "exception": false, "start_time": "2023-04-22T13:59:30.484718", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting tslearn\r\n", "  Downloading tslearn-*******-py3-none-any.whl (358 kB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m358.2/358.2 kB\u001b[0m \u001b[31m10.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hRequirement already satisfied: plotly in /opt/conda/lib/python3.7/site-packages (5.13.0)\r\n", "Requirement already satisfied: numpy in /opt/conda/lib/python3.7/site-packages (from tslearn) (1.21.6)\r\n", "Requirement already satisfied: scikit-learn in /opt/conda/lib/python3.7/site-packages (from tslearn) (1.0.2)\r\n", "Requirement already satisfied: numba in /opt/conda/lib/python3.7/site-packages (from tslearn) (0.56.4)\r\n", "Requirement already satisfied: scipy in /opt/conda/lib/python3.7/site-packages (from tslearn) (1.7.3)\r\n", "Requirement already satisfied: joblib in /opt/conda/lib/python3.7/site-packages (from tslearn) (1.2.0)\r\n", "Requirement already satisfied: tenacity>=6.2.0 in /opt/conda/lib/python3.7/site-packages (from plotly) (8.1.0)\r\n", "Requirement already satisfied: llvmlite<0.40,>=0.39.0dev0 in /opt/conda/lib/python3.7/site-packages (from numba->tslearn) (0.39.1)\r\n", "Requirement already satisfied: importlib-metadata in /opt/conda/lib/python3.7/site-packages (from numba->tslearn) (4.11.4)\r\n", "Requirement already satisfied: setuptools in /opt/conda/lib/python3.7/site-packages (from numba->tslearn) (59.8.0)\r\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /opt/conda/lib/python3.7/site-packages (from scikit-learn->tslearn) (3.1.0)\r\n", "Requirement already satisfied: typing-extensions>=3.6.4 in /opt/conda/lib/python3.7/site-packages (from importlib-metadata->numba->tslearn) (4.4.0)\r\n", "Requirement already satisfied: zipp>=0.5 in /opt/conda/lib/python3.7/site-packages (from importlib-metadata->numba->tslearn) (3.11.0)\r\n", "Installing collected packages: tslearn\r\n", "Successfully installed tslearn-*******\r\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\r\n", "\u001b[0m"]}], "source": ["!pip install tslearn plotly"]}, {"cell_type": "code", "execution_count": 4, "id": "4e9e361e", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:41.802560Z", "iopub.status.busy": "2023-04-22T13:59:41.802181Z", "iopub.status.idle": "2023-04-22T13:59:41.807342Z", "shell.execute_reply": "2023-04-22T13:59:41.806216Z"}, "papermill": {"duration": 0.017282, "end_time": "2023-04-22T13:59:41.809849", "exception": false, "start_time": "2023-04-22T13:59:41.792567", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# mode = \"debugging\"\n", "mode = \"training\"\n", "# mode = \"submission\""]}, {"cell_type": "markdown", "id": "9bfba704", "metadata": {"papermill": {"duration": 0.007393, "end_time": "2023-04-22T13:59:41.824880", "exception": false, "start_time": "2023-04-22T13:59:41.817487", "status": "completed"}, "tags": []}, "source": ["## Understanding the data"]}, {"cell_type": "code", "execution_count": 5, "id": "f12891d3", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:41.842600Z", "iopub.status.busy": "2023-04-22T13:59:41.842287Z", "iopub.status.idle": "2023-04-22T13:59:42.088380Z", "shell.execute_reply": "2023-04-22T13:59:42.087013Z"}, "papermill": {"duration": 0.257115, "end_time": "2023-04-22T13:59:42.090722", "exception": false, "start_time": "2023-04-22T13:59:41.833607", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(94477, 4)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>path</th>\n", "      <th>participant_id</th>\n", "      <th>sequence_id</th>\n", "      <th>sign</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>train_landmark_files/26734/1000035562.parquet</td>\n", "      <td>26734</td>\n", "      <td>1000035562</td>\n", "      <td>blow</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>train_landmark_files/28656/1000106739.parquet</td>\n", "      <td>28656</td>\n", "      <td>1000106739</td>\n", "      <td>wait</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>train_landmark_files/16069/100015657.parquet</td>\n", "      <td>16069</td>\n", "      <td>100015657</td>\n", "      <td>cloud</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>train_landmark_files/25571/1000210073.parquet</td>\n", "      <td>25571</td>\n", "      <td>1000210073</td>\n", "      <td>bird</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>train_landmark_files/62590/1000240708.parquet</td>\n", "      <td>62590</td>\n", "      <td>1000240708</td>\n", "      <td>owie</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>train_landmark_files/26734/1000241583.parquet</td>\n", "      <td>26734</td>\n", "      <td>1000241583</td>\n", "      <td>duck</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>train_landmark_files/26734/1000255522.parquet</td>\n", "      <td>26734</td>\n", "      <td>1000255522</td>\n", "      <td>minemy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>train_landmark_files/32319/1000278229.parquet</td>\n", "      <td>32319</td>\n", "      <td>1000278229</td>\n", "      <td>lips</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>train_landmark_files/37055/100035691.parquet</td>\n", "      <td>37055</td>\n", "      <td>100035691</td>\n", "      <td>flower</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>train_landmark_files/29302/100039661.parquet</td>\n", "      <td>29302</td>\n", "      <td>100039661</td>\n", "      <td>time</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            path  participant_id  sequence_id  \\\n", "0  train_landmark_files/26734/1000035562.parquet           26734   1000035562   \n", "1  train_landmark_files/28656/1000106739.parquet           28656   1000106739   \n", "2   train_landmark_files/16069/100015657.parquet           16069    100015657   \n", "3  train_landmark_files/25571/1000210073.parquet           25571   1000210073   \n", "4  train_landmark_files/62590/1000240708.parquet           62590   1000240708   \n", "5  train_landmark_files/26734/1000241583.parquet           26734   1000241583   \n", "6  train_landmark_files/26734/1000255522.parquet           26734   1000255522   \n", "7  train_landmark_files/32319/1000278229.parquet           32319   1000278229   \n", "8   train_landmark_files/37055/100035691.parquet           37055    100035691   \n", "9   train_landmark_files/29302/100039661.parquet           29302    100039661   \n", "\n", "     sign  \n", "0    blow  \n", "1    wait  \n", "2   cloud  \n", "3    bird  \n", "4    owie  \n", "5    duck  \n", "6  minemy  \n", "7    lips  \n", "8  flower  \n", "9    time  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train = pd.read_csv(\"/kaggle/input/asl-signs/train.csv\")\n", "print(df_train.shape)\n", "df_train.head(10)"]}, {"cell_type": "code", "execution_count": 6, "id": "c9109769", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:42.108007Z", "iopub.status.busy": "2023-04-22T13:59:42.107653Z", "iopub.status.idle": "2023-04-22T13:59:42.118690Z", "shell.execute_reply": "2023-04-22T13:59:42.117519Z"}, "papermill": {"duration": 0.022115, "end_time": "2023-04-22T13:59:42.121029", "exception": false, "start_time": "2023-04-22T13:59:42.098914", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['TV', 'after', 'airplane', 'all', 'alligator', 'animal', 'another', 'any', 'apple', 'arm', 'aunt', 'awake', 'backyard', 'bad', 'balloon', 'bath', 'because', 'bed', 'bedroom', 'bee', 'before', 'beside', 'better', 'bird', 'black', 'blow', 'blue', 'boat', 'book', 'boy', 'brother', 'brown', 'bug', 'bye', 'callonphone', 'can', 'car', 'carrot', 'cat', 'cereal', 'chair', 'cheek', 'child', 'chin', 'chocolate', 'clean', 'close', 'closet', 'cloud', 'clown', 'cow', 'cowboy', 'cry', 'cut', 'cute', 'dad', 'dance', 'dirty', 'dog', 'doll', 'donkey', 'down', 'drawer', 'drink', 'drop', 'dry', 'dryer', 'duck', 'ear', 'elephant', 'empty', 'every', 'eye', 'face', 'fall', 'farm', 'fast', 'feet', 'find', 'fine', 'finger', 'finish', 'fireman', 'first', 'fish', 'flag', 'flower', 'food', 'for', 'frenchfries', 'frog', 'garbage', 'gift', 'giraffe', 'girl', 'give', 'glasswindow', 'go', 'goose', 'grandma', 'grandpa', 'grass', 'green', 'gum', 'hair', 'happy', 'hat', 'hate', 'have', 'haveto', 'head', 'hear', 'helicopter', 'hello', 'hen', 'hesheit', 'hide', 'high', 'home', 'horse', 'hot', 'hungry', 'icecream', 'if', 'into', 'jacket', 'jeans', 'jump', 'kiss', 'kitty', 'lamp', 'later', 'like', 'lion', 'lips', 'listen', 'look', 'loud', 'mad', 'make', 'man', 'many', 'milk', 'minemy', 'mitten', 'mom', 'moon', 'morning', 'mouse', 'mouth', 'nap', 'napkin', 'night', 'no', 'noisy', 'nose', 'not', 'now', 'nuts', 'old', 'on', 'open', 'orange', 'outside', 'owie', 'owl', 'pajamas', 'pen', 'pencil', 'penny', 'person', 'pig', 'pizza', 'please', 'police', 'pool', 'potty', 'pretend', 'pretty', 'puppy', 'puzzle', 'quiet', 'radio', 'rain', 'read', 'red', 'refrigerator', 'ride', 'room', 'sad', 'same', 'say', 'scissors', 'see', 'shhh', 'shirt', 'shoe', 'shower', 'sick', 'sleep', 'sleepy', 'smile', 'snack', 'snow', 'stairs', 'stay', 'sticky', 'store', 'story', 'stuck', 'sun', 'table', 'talk', 'taste', 'thankyou', 'that', 'there', 'think', 'thirsty', 'tiger', 'time', 'tomorrow', 'tongue', 'tooth', 'toothbrush', 'touch', 'toy', 'tree', 'uncle', 'underwear', 'up', 'vacuum', 'wait', 'wake', 'water', 'wet', 'weus', 'where', 'white', 'who', 'why', 'will', 'wolf', 'yellow', 'yes', 'yesterday', 'yourself', 'yucky', 'zebra', 'zipper']\n"]}], "source": ["json_file_path = \"/kaggle/input/asl-signs/sign_to_prediction_index_map.json\"\n", "with open(json_file_path, 'r') as j:\n", "     sign_dict = json.loads(j.read())\n", "        \n", "ordered_signs = list(sign_dict.keys())\n", "print(ordered_signs)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "a4b9ddfa", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:42.137855Z", "iopub.status.busy": "2023-04-22T13:59:42.137526Z", "iopub.status.idle": "2023-04-22T13:59:42.146559Z", "shell.execute_reply": "2023-04-22T13:59:42.145670Z"}, "papermill": {"duration": 0.019971, "end_time": "2023-04-22T13:59:42.148891", "exception": false, "start_time": "2023-04-22T13:59:42.128920", "status": "completed"}, "tags": []}, "outputs": [], "source": ["ROWS_PER_FRAME = 543  # number of landmarks per frame\n", "\n", "def load_relevant_data_subset(pq_path):\n", "    data_columns = ['x', 'y', 'z']\n", "    data = pd.read_parquet(pq_path, columns=data_columns).fillna(0)\n", "    n_frames = int(len(data) / ROWS_PER_FRAME)\n", "    data = data.values.reshape(n_frames, ROWS_PER_FRAME, len(data_columns))\n", "    return data.astype(np.float32)\n", "\n", "def load_relevant_data(pq_path):\n", "    data = pd.read_parquet(pq_path).fillna(0)\n", "    return data\n"]}, {"cell_type": "code", "execution_count": 8, "id": "6183fd10", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:42.165613Z", "iopub.status.busy": "2023-04-22T13:59:42.165325Z", "iopub.status.idle": "2023-04-22T13:59:42.284032Z", "shell.execute_reply": "2023-04-22T13:59:42.282738Z"}, "papermill": {"duration": 0.129749, "end_time": "2023-04-22T13:59:42.286348", "exception": false, "start_time": "2023-04-22T13:59:42.156599", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame</th>\n", "      <th>row_id</th>\n", "      <th>type</th>\n", "      <th>landmark_index</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>z</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17</td>\n", "      <td>17-face-0</td>\n", "      <td>face</td>\n", "      <td>0</td>\n", "      <td>0.495870</td>\n", "      <td>0.478694</td>\n", "      <td>-0.037412</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>17</td>\n", "      <td>17-face-1</td>\n", "      <td>face</td>\n", "      <td>1</td>\n", "      <td>0.492222</td>\n", "      <td>0.447209</td>\n", "      <td>-0.067939</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17</td>\n", "      <td>17-face-2</td>\n", "      <td>face</td>\n", "      <td>2</td>\n", "      <td>0.492067</td>\n", "      <td>0.457237</td>\n", "      <td>-0.035722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17</td>\n", "      <td>17-face-3</td>\n", "      <td>face</td>\n", "      <td>3</td>\n", "      <td>0.480419</td>\n", "      <td>0.415996</td>\n", "      <td>-0.050779</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>17</td>\n", "      <td>17-face-4</td>\n", "      <td>face</td>\n", "      <td>4</td>\n", "      <td>0.492035</td>\n", "      <td>0.437453</td>\n", "      <td>-0.072314</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>17</td>\n", "      <td>17-face-5</td>\n", "      <td>face</td>\n", "      <td>5</td>\n", "      <td>0.491820</td>\n", "      <td>0.424721</td>\n", "      <td>-0.067133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>17</td>\n", "      <td>17-face-6</td>\n", "      <td>face</td>\n", "      <td>6</td>\n", "      <td>0.491240</td>\n", "      <td>0.394258</td>\n", "      <td>-0.032263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>17</td>\n", "      <td>17-face-7</td>\n", "      <td>face</td>\n", "      <td>7</td>\n", "      <td>0.398341</td>\n", "      <td>0.395734</td>\n", "      <td>0.017706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>17</td>\n", "      <td>17-face-8</td>\n", "      <td>face</td>\n", "      <td>8</td>\n", "      <td>0.490602</td>\n", "      <td>0.367059</td>\n", "      <td>-0.023201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>17</td>\n", "      <td>17-face-9</td>\n", "      <td>face</td>\n", "      <td>9</td>\n", "      <td>0.490210</td>\n", "      <td>0.353400</td>\n", "      <td>-0.025720</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   frame     row_id  type  landmark_index         x         y         z\n", "0     17  17-face-0  face               0  0.495870  0.478694 -0.037412\n", "1     17  17-face-1  face               1  0.492222  0.447209 -0.067939\n", "2     17  17-face-2  face               2  0.492067  0.457237 -0.035722\n", "3     17  17-face-3  face               3  0.480419  0.415996 -0.050779\n", "4     17  17-face-4  face               4  0.492035  0.437453 -0.072314\n", "5     17  17-face-5  face               5  0.491820  0.424721 -0.067133\n", "6     17  17-face-6  face               6  0.491240  0.394258 -0.032263\n", "7     17  17-face-7  face               7  0.398341  0.395734  0.017706\n", "8     17  17-face-8  face               8  0.490602  0.367059 -0.023201\n", "9     17  17-face-9  face               9  0.490210  0.353400 -0.025720"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["idx_plot = 3\n", "path_show = \"/kaggle/input/asl-signs/\"+df_train['path'].values[idx_plot]\n", "sign_plot = df_train['sign'].values[idx_plot]\n", "path_example = path_show.replace(\"_\", \"_\")\n", "\n", "df = load_relevant_data(path_show)\n", "df.head(10)"]}, {"cell_type": "code", "execution_count": 9, "id": "678462f5", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:42.304966Z", "iopub.status.busy": "2023-04-22T13:59:42.304266Z", "iopub.status.idle": "2023-04-22T13:59:42.329501Z", "shell.execute_reply": "2023-04-22T13:59:42.327738Z"}, "papermill": {"duration": 0.036859, "end_time": "2023-04-22T13:59:42.331967", "exception": false, "start_time": "2023-04-22T13:59:42.295108", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Number of frames: 11\n", "Keypoints: 543\n", "<PERSON>, Y Z postions: 3\n", "Total number of datapoints in this sequence: 17919\n", "\n", "Pose landmarks: 33\n", "Face landmarks: 468\n", "Right hand landmarks: 21\n", "Left hand landmarks: 21\n", "Total landmarks/keypoints:  543\n"]}], "source": ["path_show = \"/kaggle/input/asl-signs/\"+df_train['path'].values[1]\n", "sign_show = df_train['sign'].values[1]\n", "\n", "df_example = load_relevant_data_subset(path_show)\n", "\n", "frames = df_example.shape[0]\n", "keypoints = df_example.shape[1]\n", "position = df_example.shape[2]\n", "\n", "print(\"\\nNumber of frames:\", frames)\n", "print(\"Keypoints:\", keypoints)\n", "print(\"X, Y Z postions:\", position)\n", "print(\"Total number of datapoints in this sequence:\", np.prod(df_example.shape))\n", "\n", "\n", "pose_landmarks = 33\n", "face_landmarks = 468\n", "right_hand_landmarks = 21\n", "start_left_hand = face_landmarks\n", "left_hand_landmarks = 21\n", "start_right_hand = face_landmarks + left_hand_landmarks + pose_landmarks\n", "total_landmarks = pose_landmarks + face_landmarks + right_hand_landmarks + left_hand_landmarks\n", "\n", "\n", "print(\"\\nPose landmarks:\", pose_landmarks)\n", "print(\"Face landmarks:\", face_landmarks)\n", "print(\"Right hand landmarks:\", right_hand_landmarks)\n", "print(\"Left hand landmarks:\", left_hand_landmarks)\n", "print(\"Total landmarks/keypoints: \", total_landmarks)"]}, {"cell_type": "code", "execution_count": 10, "id": "470a03a4", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:42.352142Z", "iopub.status.busy": "2023-04-22T13:59:42.351070Z", "iopub.status.idle": "2023-04-22T13:59:42.363731Z", "shell.execute_reply": "2023-04-22T13:59:42.362702Z"}, "papermill": {"duration": 0.024641, "end_time": "2023-04-22T13:59:42.365992", "exception": false, "start_time": "2023-04-22T13:59:42.341351", "status": "completed"}, "tags": []}, "outputs": [], "source": ["from tqdm import tqdm\n", "\n", "max_sequence_length = 32\n", "lip_marks = [61, 185, 40, 39, 37, 0, 267, 269, 270, 409, 291, 78, 191, 80, 81, 82, 13, 312, 311, 310, 415, 308, 95, 88, 178, 87, 14, 317, 402, 318, 324, 146, 91, 181, 84, 17, 314, 405, 321, 375]  \n", "\n", "lips = lip_marks\n", "left_hand = [*range(start_left_hand, start_left_hand+left_hand_landmarks, 1)]\n", "right_hand = [*range(start_right_hand, start_right_hand+right_hand_landmarks, 1)]\n", "meaningful_keypoints = lips + left_hand + right_hand\n", "input_length = len(meaningful_keypoints)*3\n", "\n", "def get_data(file_paths, y_sign):\n", "    \n", "    X = np.empty((file_paths.shape[0], max_sequence_length, len(meaningful_keypoints)*3), dtype=float)\n", "\n", "    for i in tqdm(range(file_paths.shape[0])):\n", "        file_name = \"/kaggle/input/asl-signs/\"+file_paths[i]\n", "        data = load_relevant_data_subset(file_name)\n", "        \n", "        data = data[:, meaningful_keypoints]\n", "        \n", "        if data.shape[0] < max_sequence_length:\n", "            rows = max_sequence_length - data.shape[0]\n", "            data = np.append(np.zeros((rows, len(meaningful_keypoints), 3)), data, axis=0)\n", "        elif data.shape[0] > max_sequence_length:\n", "            data = data[-(max_sequence_length):]\n", "\n", "        X[i] = data.reshape(max_sequence_length, len(meaningful_keypoints)*3, order='F')\n", "        \n", "        del data\n", "        \n", "    X = np.asarray(X).astype(np.float32)\n", "        \n", "    y = []\n", "    for sign in y_sign:\n", "        y.append(sign_dict[sign])\n", "\n", "    y = np.array(y, dtype=int)\n", "\n", "    return X, y"]}, {"cell_type": "markdown", "id": "e9ec047d", "metadata": {"papermill": {"duration": 0.008414, "end_time": "2023-04-22T13:59:42.382866", "exception": false, "start_time": "2023-04-22T13:59:42.374452", "status": "completed"}, "tags": []}, "source": ["# Training the classifier"]}, {"cell_type": "markdown", "id": "356efeb8", "metadata": {"papermill": {"duration": 0.008707, "end_time": "2023-04-22T13:59:42.400247", "exception": false, "start_time": "2023-04-22T13:59:42.391540", "status": "completed"}, "tags": []}, "source": ["## LSTM\n", "Lets start with a simple LSTM system"]}, {"cell_type": "code", "execution_count": 11, "id": "0b236612", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:42.419746Z", "iopub.status.busy": "2023-04-22T13:59:42.418833Z", "iopub.status.idle": "2023-04-22T13:59:50.394007Z", "shell.execute_reply": "2023-04-22T13:59:50.392772Z"}, "papermill": {"duration": 7.987602, "end_time": "2023-04-22T13:59:50.396718", "exception": false, "start_time": "2023-04-22T13:59:42.409116", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models, Input, optimizers\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from tensorflow.keras.utils import pad_sequences"]}, {"cell_type": "code", "execution_count": 12, "id": "3dd3186e", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:50.415263Z", "iopub.status.busy": "2023-04-22T13:59:50.414585Z", "iopub.status.idle": "2023-04-22T13:59:50.610773Z", "shell.execute_reply": "2023-04-22T13:59:50.609700Z"}, "papermill": {"duration": 0.208294, "end_time": "2023-04-22T13:59:50.613430", "exception": false, "start_time": "2023-04-22T13:59:50.405136", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def scaled_dot_product(q,k,v, softmax):\n", "    #calculates Q . K(transpose)\n", "    qkt = tf.matmul(q,k,transpose_b=True)\n", "    #caculates scaling factor\n", "    dk = tf.math.sqrt(tf.cast(q.shape[-1],dtype=tf.float32))\n", "    scaled_qkt = qkt/dk\n", "    softmax = softmax(scaled_qkt)\n", "    \n", "    z = tf.matmul(softmax,v)\n", "    #shape: (m,Tx,depth), same shape as q,k,v\n", "    return z\n", "\n", "class MultiHeadAttention(tf.keras.layers.Layer):\n", "    def __init__(self,d_model,num_of_heads):\n", "        super(<PERSON>H<PERSON><PERSON><PERSON><PERSON>,self).__init__()\n", "        self.d_model = d_model\n", "        self.num_of_heads = num_of_heads\n", "        self.depth = d_model//num_of_heads\n", "        self.wq = [tf.keras.layers.Dense(self.depth) for i in range(num_of_heads)]\n", "        self.wk = [tf.keras.layers.Dense(self.depth) for i in range(num_of_heads)]\n", "        self.wv = [tf.keras.layers.Dense(self.depth) for i in range(num_of_heads)]\n", "        self.wo = tf.keras.layers.Dense(d_model)\n", "        self.softmax = tf.keras.layers.Softmax()\n", "        \n", "    def call(self,x):\n", "        \n", "        multi_attn = []\n", "        for i in range(self.num_of_heads):\n", "            Q = self.wq[i](x)\n", "            K = self.wk[i](x)\n", "            V = self.wv[i](x)\n", "            multi_attn.append(scaled_dot_product(Q,K,V, self.softmax))\n", "            \n", "        multi_head = tf.concat(multi_attn,axis=-1)\n", "        multi_head_attention = self.wo(multi_head)\n", "        return multi_head_attention"]}, {"cell_type": "code", "execution_count": 13, "id": "ad5ef7fa", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:50.632613Z", "iopub.status.busy": "2023-04-22T13:59:50.632267Z", "iopub.status.idle": "2023-04-22T13:59:53.937600Z", "shell.execute_reply": "2023-04-22T13:59:53.936802Z"}, "papermill": {"duration": 3.339318, "end_time": "2023-04-22T13:59:53.961504", "exception": false, "start_time": "2023-04-22T13:59:50.622186", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"model\"\n", "_________________________________________________________________\n", " Layer (type)                Output Shape              Param #   \n", "=================================================================\n", " input_1 (InputLayer)        [(None, None, 246)]       0         \n", "                                                                 \n", " dense (<PERSON><PERSON>)               (None, None, 256)         63232     \n", "                                                                 \n", " layer_normalization (LayerN  (None, None, 256)        512       \n", " ormalization)                                                   \n", "                                                                 \n", " activation (Activation)     (None, None, 256)         0         \n", "                                                                 \n", " dropout (Dropout)           (None, None, 256)         0         \n", "                                                                 \n", " dense_1 (<PERSON><PERSON>)             (None, None, 128)         32896     \n", "                                                                 \n", " layer_normalization_1 (<PERSON><PERSON>  (None, None, 128)        256       \n", " rNormalization)                                                 \n", "                                                                 \n", " activation_1 (Activation)   (None, None, 128)         0         \n", "                                                                 \n", " dropout_1 (Dropout)         (None, None, 128)         0         \n", "                                                                 \n", " dense_2 (<PERSON><PERSON>)             (None, None, 256)         33024     \n", "                                                                 \n", " layer_normalization_2 (<PERSON><PERSON>  (None, None, 256)        512       \n", " rNormalization)                                                 \n", "                                                                 \n", " activation_2 (Activation)   (None, None, 256)         0         \n", "                                                                 \n", " dropout_2 (Dropout)         (None, None, 256)         0         \n", "                                                                 \n", " lstm (LSTM)                 (None, 256)               525312    \n", "                                                                 \n", " outputs (Dense)             (None, 250)               64250     \n", "                                                                 \n", "=================================================================\n", "Total params: 719,994\n", "Trainable params: 719,994\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["  \n", "# a single dense block followed by a normalization block and relu activation\n", "def dense_block(units):\n", "    fc = layers.Dense(units)\n", "    norm = layers.LayerNormalization()\n", "    act = layers.Activation(\"relu\")\n", "    drop = layers.Dropout(0.05)\n", "    return lambda x: drop(act(norm(fc(x))))\n", "\n", "# transformer blocks\n", "def transformer_block(key_dim, x):\n", "    mha = MultiHeadAttention(key_dim, 8)(x)\n", "    add1 = layers.add([mha, x])\n", "    norm1 = layers.LayerNormalization()(add1)\n", "\n", "    fc = layers.Dense(key_dim, activation=\"relu\")(norm1)\n", "    add2 = tf.math.add(fc, norm1)\n", "    norm2 = layers.LayerNormalization()(add2)\n", "\n", "    return norm2\n", "\n", "# the final dense block for the classification\n", "def classifier_lstm(units):\n", "    lstm = layers.LSTM(units)\n", "    out = layers.Dense(250, activation=\"softmax\", name=\"outputs\")\n", "    return lambda x: out(lstm(x))\n", "    \n", "def classifier_transformer():\n", "    dense = layers.Dense(256, activation=\"relu\")\n", "    drop = layers.Dropout(0.1)\n", "    \n", "    out = layers.Dense(250, activation=\"softmax\", name=\"outputs\")\n", "    return lambda x: out(drop(dense(x)))\n", "\n", "inputs = tf.keras.Input(shape=(None, input_length), ragged=True)\n", "# choose the number of nodes per layer\n", "embedding_units = [256, 128, 256] # tune this\n", "transformer_units = []#, 512, 512]\n", "\n", "# # dense encoder model\n", "x = inputs\n", "for n in embedding_units:\n", "    x = dense_block(n)(x)\n", "    \n", "for t in transformer_units:\n", "    x = transformer_block(t, x)\n", "\n", "# classifier layer\n", "if len(transformer_units) > 0:\n", "    # Pooling\n", "    x = tf.math.reduce_sum(x, axis=1)\n", "    out = classifier_transformer()(x)\n", "else:\n", "    out = classifier_lstm(embedding_units[-1])(x)\n", "\n", "\n", "model = tf.keras.Model(inputs=inputs, outputs=out)\n", "model.summary()"]}, {"cell_type": "code", "execution_count": 14, "id": "52105aa8", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:53.983628Z", "iopub.status.busy": "2023-04-22T13:59:53.983274Z", "iopub.status.idle": "2023-04-22T13:59:54.044644Z", "shell.execute_reply": "2023-04-22T13:59:54.043493Z"}, "papermill": {"duration": 0.074981, "end_time": "2023-04-22T13:59:54.046876", "exception": false, "start_time": "2023-04-22T13:59:53.971895", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[8050, 12250, 15750, 18550, 21000]\n"]}], "source": ["# add a decreasing learning rate scheduler to help convergence\n", "batch_size = 256\n", "validation_percentage = 0.05\n", "steps_per_epoch = int(94477*(1-validation_percentage)) // batch_size\n", "boundaries = [steps_per_epoch * n for n in [23, 35, 45, 53, 60]]\n", "print(boundaries)\n", "values = [1e-3,1e-4,1e-5,1e-6,1e-7,1e-8]\n", "lr_sched = optimizers.schedules.PiecewiseConstantDecay(boundaries, values)\n", "\n", "optimizer = optimizers.<PERSON>(lr_sched)\n", "# optimizer = optimizers.<PERSON>()\n", "\n", "model.compile(optimizer=optimizer,\n", "              loss=tf.keras.losses.SparseCategoricalCrossentropy(name=\"loss\"),\n", "              metrics=[\"accuracy\",\"sparse_top_k_categorical_accuracy\"])"]}, {"cell_type": "code", "execution_count": 15, "id": "e7b5112c", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:54.069657Z", "iopub.status.busy": "2023-04-22T13:59:54.068676Z", "iopub.status.idle": "2023-04-22T13:59:54.074940Z", "shell.execute_reply": "2023-04-22T13:59:54.073883Z"}, "papermill": {"duration": 0.019615, "end_time": "2023-04-22T13:59:54.077128", "exception": false, "start_time": "2023-04-22T13:59:54.057513", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def get_callbacks():\n", "    return [\n", "        tf.keras.callbacks.EarlyStopping(\n", "            monitor=\"val_accuracy\",\n", "            patience = 10,\n", "            restore_best_weights=True\n", "        ),\n", "        tf.keras.callbacks.ReduceLROnPlateau(\n", "            monitor = \"val_accuracy\",\n", "            factor = 0.2,\n", "            patience = 5\n", "        ),\n", "    ]"]}, {"cell_type": "code", "execution_count": 16, "id": "ae8f578c", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T13:59:54.100599Z", "iopub.status.busy": "2023-04-22T13:59:54.099834Z", "iopub.status.idle": "2023-04-22T14:30:45.755989Z", "shell.execute_reply": "2023-04-22T14:30:45.754861Z"}, "papermill": {"duration": 1851.671073, "end_time": "2023-04-22T14:30:45.758699", "exception": false, "start_time": "2023-04-22T13:59:54.087626", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 94477/94477 [25:48<00:00, 61.01it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(89753, 32, 246)\n", "(89753,)\n", "Epoch 1/500\n", "351/351 - 19s - loss: 4.8013 - accuracy: 0.0527 - sparse_top_k_categorical_accuracy: 0.1686 - val_loss: 4.0263 - val_accuracy: 0.1287 - val_sparse_top_k_categorical_accuracy: 0.3484 - lr: 0.0010 - 19s/epoch - 56ms/step\n", "Epoch 2/500\n", "351/351 - 8s - loss: 3.5687 - accuracy: 0.1994 - sparse_top_k_categorical_accuracy: 0.4668 - val_loss: 3.1741 - val_accuracy: 0.2733 - val_sparse_top_k_categorical_accuracy: 0.5624 - lr: 0.0010 - 8s/epoch - 23ms/step\n", "Epoch 3/500\n", "351/351 - 8s - loss: 2.9753 - accuracy: 0.3073 - sparse_top_k_categorical_accuracy: 0.6010 - val_loss: 2.6897 - val_accuracy: 0.3732 - val_sparse_top_k_categorical_accuracy: 0.6641 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 4/500\n", "351/351 - 8s - loss: 2.5785 - accuracy: 0.3892 - sparse_top_k_categorical_accuracy: 0.6815 - val_loss: 2.4126 - val_accuracy: 0.4333 - val_sparse_top_k_categorical_accuracy: 0.7125 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 5/500\n", "351/351 - 8s - loss: 2.3339 - accuracy: 0.4408 - sparse_top_k_categorical_accuracy: 0.7234 - val_loss: 2.2394 - val_accuracy: 0.4723 - val_sparse_top_k_categorical_accuracy: 0.7354 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 6/500\n", "351/351 - 8s - loss: 2.1295 - accuracy: 0.4864 - sparse_top_k_categorical_accuracy: 0.7557 - val_loss: 2.2119 - val_accuracy: 0.4668 - val_sparse_top_k_categorical_accuracy: 0.7451 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 7/500\n", "351/351 - 8s - loss: 2.0114 - accuracy: 0.5116 - sparse_top_k_categorical_accuracy: 0.7738 - val_loss: 2.0321 - val_accuracy: 0.5123 - val_sparse_top_k_categorical_accuracy: 0.7693 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 8/500\n", "351/351 - 8s - loss: 1.8962 - accuracy: 0.5387 - sparse_top_k_categorical_accuracy: 0.7917 - val_loss: 1.9950 - val_accuracy: 0.5220 - val_sparse_top_k_categorical_accuracy: 0.7735 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 9/500\n", "351/351 - 8s - loss: 1.7897 - accuracy: 0.5630 - sparse_top_k_categorical_accuracy: 0.8056 - val_loss: 1.8685 - val_accuracy: 0.5567 - val_sparse_top_k_categorical_accuracy: 0.7900 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 10/500\n", "351/351 - 8s - loss: 1.7121 - accuracy: 0.5816 - sparse_top_k_categorical_accuracy: 0.8167 - val_loss: 1.8172 - val_accuracy: 0.5593 - val_sparse_top_k_categorical_accuracy: 0.8008 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 11/500\n", "351/351 - 8s - loss: 1.6463 - accuracy: 0.5948 - sparse_top_k_categorical_accuracy: 0.8263 - val_loss: 1.7815 - val_accuracy: 0.5669 - val_sparse_top_k_categorical_accuracy: 0.8027 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 12/500\n", "351/351 - 8s - loss: 1.5608 - accuracy: 0.6166 - sparse_top_k_categorical_accuracy: 0.8372 - val_loss: 1.7692 - val_accuracy: 0.5715 - val_sparse_top_k_categorical_accuracy: 0.8093 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 13/500\n", "351/351 - 8s - loss: 1.4939 - accuracy: 0.6324 - sparse_top_k_categorical_accuracy: 0.8451 - val_loss: 1.7114 - val_accuracy: 0.5836 - val_sparse_top_k_categorical_accuracy: 0.8129 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 14/500\n", "351/351 - 7s - loss: 1.4411 - accuracy: 0.6431 - sparse_top_k_categorical_accuracy: 0.8520 - val_loss: 1.6567 - val_accuracy: 0.5991 - val_sparse_top_k_categorical_accuracy: 0.8188 - lr: 0.0010 - 7s/epoch - 21ms/step\n", "Epoch 15/500\n", "351/351 - 7s - loss: 1.4026 - accuracy: 0.6514 - sparse_top_k_categorical_accuracy: 0.8558 - val_loss: 1.6489 - val_accuracy: 0.6014 - val_sparse_top_k_categorical_accuracy: 0.8167 - lr: 0.0010 - 7s/epoch - 21ms/step\n", "Epoch 16/500\n", "351/351 - 7s - loss: 1.3480 - accuracy: 0.6648 - sparse_top_k_categorical_accuracy: 0.8626 - val_loss: 1.6218 - val_accuracy: 0.5993 - val_sparse_top_k_categorical_accuracy: 0.8245 - lr: 0.0010 - 7s/epoch - 21ms/step\n", "Epoch 17/500\n", "351/351 - 7s - loss: 1.3022 - accuracy: 0.6758 - sparse_top_k_categorical_accuracy: 0.8686 - val_loss: 1.6327 - val_accuracy: 0.6090 - val_sparse_top_k_categorical_accuracy: 0.8235 - lr: 0.0010 - 7s/epoch - 21ms/step\n", "Epoch 18/500\n", "351/351 - 8s - loss: 1.2532 - accuracy: 0.6882 - sparse_top_k_categorical_accuracy: 0.8737 - val_loss: 1.6546 - val_accuracy: 0.5967 - val_sparse_top_k_categorical_accuracy: 0.8222 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 19/500\n", "351/351 - 7s - loss: 1.2218 - accuracy: 0.6935 - sparse_top_k_categorical_accuracy: 0.8786 - val_loss: 1.6223 - val_accuracy: 0.6124 - val_sparse_top_k_categorical_accuracy: 0.8256 - lr: 0.0010 - 7s/epoch - 21ms/step\n", "Epoch 20/500\n", "351/351 - 7s - loss: 1.1752 - accuracy: 0.7066 - sparse_top_k_categorical_accuracy: 0.8835 - val_loss: 1.5934 - val_accuracy: 0.6120 - val_sparse_top_k_categorical_accuracy: 0.8251 - lr: 0.0010 - 7s/epoch - 21ms/step\n", "Epoch 21/500\n", "351/351 - 8s - loss: 1.1374 - accuracy: 0.7166 - sparse_top_k_categorical_accuracy: 0.8892 - val_loss: 1.6219 - val_accuracy: 0.6124 - val_sparse_top_k_categorical_accuracy: 0.8232 - lr: 0.0010 - 8s/epoch - 22ms/step\n", "Epoch 22/500\n", "351/351 - 8s - loss: 1.1004 - accuracy: 0.7260 - sparse_top_k_categorical_accuracy: 0.8928 - val_loss: 1.6058 - val_accuracy: 0.6147 - val_sparse_top_k_categorical_accuracy: 0.8307 - lr: 0.0010 - 8s/epoch - 21ms/step\n", "Epoch 23/500\n", "351/351 - 7s - loss: 1.0543 - accuracy: 0.7360 - sparse_top_k_categorical_accuracy: 0.8978 - val_loss: 1.5060 - val_accuracy: 0.6365 - val_sparse_top_k_categorical_accuracy: 0.8421 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 24/500\n", "351/351 - 7s - loss: 0.8628 - accuracy: 0.7963 - sparse_top_k_categorical_accuracy: 0.9156 - val_loss: 1.4663 - val_accuracy: 0.6507 - val_sparse_top_k_categorical_accuracy: 0.8442 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 25/500\n", "351/351 - 7s - loss: 0.8279 - accuracy: 0.8074 - sparse_top_k_categorical_accuracy: 0.9179 - val_loss: 1.4702 - val_accuracy: 0.6528 - val_sparse_top_k_categorical_accuracy: 0.8461 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 26/500\n", "351/351 - 8s - loss: 0.8148 - accuracy: 0.8111 - sparse_top_k_categorical_accuracy: 0.9194 - val_loss: 1.4646 - val_accuracy: 0.6547 - val_sparse_top_k_categorical_accuracy: 0.8423 - lr: 1.0000e-04 - 8s/epoch - 22ms/step\n", "Epoch 27/500\n", "351/351 - 7s - loss: 0.8028 - accuracy: 0.8138 - sparse_top_k_categorical_accuracy: 0.9213 - val_loss: 1.4655 - val_accuracy: 0.6556 - val_sparse_top_k_categorical_accuracy: 0.8438 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 28/500\n", "351/351 - 7s - loss: 0.7936 - accuracy: 0.8174 - sparse_top_k_categorical_accuracy: 0.9212 - val_loss: 1.4695 - val_accuracy: 0.6482 - val_sparse_top_k_categorical_accuracy: 0.8446 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 29/500\n", "351/351 - 7s - loss: 0.7853 - accuracy: 0.8196 - sparse_top_k_categorical_accuracy: 0.9227 - val_loss: 1.4673 - val_accuracy: 0.6528 - val_sparse_top_k_categorical_accuracy: 0.8450 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 30/500\n", "351/351 - 7s - loss: 0.7793 - accuracy: 0.8206 - sparse_top_k_categorical_accuracy: 0.9236 - val_loss: 1.4717 - val_accuracy: 0.6490 - val_sparse_top_k_categorical_accuracy: 0.8446 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 31/500\n", "351/351 - 7s - loss: 0.7715 - accuracy: 0.8231 - sparse_top_k_categorical_accuracy: 0.9248 - val_loss: 1.4706 - val_accuracy: 0.6490 - val_sparse_top_k_categorical_accuracy: 0.8434 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 32/500\n", "351/351 - 7s - loss: 0.7640 - accuracy: 0.8257 - sparse_top_k_categorical_accuracy: 0.9248 - val_loss: 1.4721 - val_accuracy: 0.6501 - val_sparse_top_k_categorical_accuracy: 0.8450 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 33/500\n", "351/351 - 7s - loss: 0.7594 - accuracy: 0.8270 - sparse_top_k_categorical_accuracy: 0.9248 - val_loss: 1.4700 - val_accuracy: 0.6511 - val_sparse_top_k_categorical_accuracy: 0.8448 - lr: 1.0000e-04 - 7s/epoch - 21ms/step\n", "Epoch 34/500\n", "351/351 - 8s - loss: 0.7532 - accuracy: 0.8293 - sparse_top_k_categorical_accuracy: 0.9259 - val_loss: 1.4701 - val_accuracy: 0.6533 - val_sparse_top_k_categorical_accuracy: 0.8442 - lr: 1.0000e-04 - 8s/epoch - 22ms/step\n", "Epoch 35/500\n", "351/351 - 7s - loss: 0.7438 - accuracy: 0.8316 - sparse_top_k_categorical_accuracy: 0.9271 - val_loss: 1.4669 - val_accuracy: 0.6533 - val_sparse_top_k_categorical_accuracy: 0.8455 - lr: 1.0000e-05 - 7s/epoch - 21ms/step\n", "Epoch 36/500\n", "351/351 - 7s - loss: 0.7228 - accuracy: 0.8375 - sparse_top_k_categorical_accuracy: 0.9292 - val_loss: 1.4666 - val_accuracy: 0.6501 - val_sparse_top_k_categorical_accuracy: 0.8455 - lr: 1.0000e-05 - 7s/epoch - 21ms/step\n", "Epoch 37/500\n", "351/351 - 7s - loss: 0.7186 - accuracy: 0.8393 - sparse_top_k_categorical_accuracy: 0.9296 - val_loss: 1.4660 - val_accuracy: 0.6537 - val_sparse_top_k_categorical_accuracy: 0.8459 - lr: 1.0000e-05 - 7s/epoch - 21ms/step\n"]}], "source": ["if mode == \"training\":\n", "    file_paths = df_train['path'].values#[:1000]\n", "    y_sign = df_train['sign'].values#[:1000]\n", "    X, y = get_data(file_paths, y_sign)\n", "\n", "    X, X_val, y, y_val = train_test_split(X, y, test_size=validation_percentage, random_state=123)\n", "\n", "    print(X.shape)\n", "    print(y.shape)\n", "\n", "    history = model.fit(X, y, \n", "                    epochs=500,\n", "                    batch_size=batch_size,\n", "                    validation_data=(X_val, y_val),\n", "                    verbose=2,\n", "                    callbacks=[get_callbacks()]\n", "                   )\n", "\n", "    loss = history.history['loss']\n", "    val_loss = history.history['val_loss']\n", "    accuracy = history.history['accuracy']\n", "    val_accuracy = history.history['val_accuracy']"]}, {"cell_type": "code", "execution_count": 17, "id": "9724f7ec", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:30:47.271639Z", "iopub.status.busy": "2023-04-22T14:30:47.271200Z", "iopub.status.idle": "2023-04-22T14:30:47.283113Z", "shell.execute_reply": "2023-04-22T14:30:47.282115Z"}, "papermill": {"duration": 0.803888, "end_time": "2023-04-22T14:30:47.285316", "exception": false, "start_time": "2023-04-22T14:30:46.481428", "status": "completed"}, "tags": []}, "outputs": [], "source": ["if mode == \"training\":\n", "    del X\n", "    del y\n", "    del X_val\n", "    del y_val"]}, {"cell_type": "code", "execution_count": 18, "id": "c19d9a30", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:30:48.776983Z", "iopub.status.busy": "2023-04-22T14:30:48.776609Z", "iopub.status.idle": "2023-04-22T14:31:19.640588Z", "shell.execute_reply": "2023-04-22T14:31:19.639348Z"}, "papermill": {"duration": 32.41505, "end_time": "2023-04-22T14:31:20.413228", "exception": false, "start_time": "2023-04-22T14:30:47.998178", "status": "completed"}, "tags": []}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjcAAAGwCAYAAABVdURTAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/NK7nSAAAACXBIWXMAAA9hAAAPYQGoP6dpAABjJklEQVR4nO3dd3wUdf7H8dembXpCKgmEEAg99CiGpoKgqCiiUlSEU09RUTnUU86zcd6BnmI9OCueiorYjlN+ap<PERSON>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***************************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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if mode == \"training\":\n", "    import matplotlib.pyplot as plt\n", "    fig = plt.figure()\n", "\n", "    plt.plot(val_accuracy, label='val_accuracy')\n", "\n", "    plt.plot(accuracy, label='accuracy')\n", "\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Accuracy')\n", "    # plt.ylim([0.5, 1])\n", "    plt.legend()\n", "\n", "#     test_loss, test_acc = model.evaluate(X,  y, verbose=1)"]}, {"cell_type": "markdown", "id": "e9fc8619", "metadata": {"papermill": {"duration": 0.711122, "end_time": "2023-04-22T14:31:21.841191", "exception": false, "start_time": "2023-04-22T14:31:21.130069", "status": "completed"}, "tags": []}, "source": ["# Convert final model in a tf.lite model\n", "Add a preprocessing pipeline and safe the model as tf.lite model"]}, {"cell_type": "code", "execution_count": 19, "id": "5312b370", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:31:23.366014Z", "iopub.status.busy": "2023-04-22T14:31:23.365644Z", "iopub.status.idle": "2023-04-22T14:31:23.375194Z", "shell.execute_reply": "2023-04-22T14:31:23.374125Z"}, "papermill": {"duration": 0.728719, "end_time": "2023-04-22T14:31:23.377418", "exception": false, "start_time": "2023-04-22T14:31:22.648699", "status": "completed"}, "tags": []}, "outputs": [], "source": ["lips = lip_marks\n", "left_hand = [*range(start_left_hand, start_left_hand+left_hand_landmarks, 1)]\n", "right_hand = [*range(start_right_hand, start_right_hand+right_hand_landmarks, 1)]\n", "meaningful_keypoints = lips + left_hand + right_hand\n", "\n", "\n", "def get_inference_model(model):\n", "    inputs = tf.keras.Input(shape=(ROWS_PER_FRAME,3), name=\"inputs\")\n", "    \n", "    # drop most of the face mesh\n", "    x = tf.gather(inputs, meaningful_keypoints, axis=1)\n", "\n", "    # fill nan\n", "    x = tf.where(tf.math.is_nan(x), tf.zeros_like(x), x)\n", "\n", "    # flatten landmark xyz coordinates ()\n", "    x = tf.concat([x[...,i] for i in range(3)], -1)\n", "\n", "    x = tf.expand_dims(x,0)\n", "    \n", "    # call trained model\n", "    out = model(x)\n", "    \n", "    # explicitly name the final (identity) layer for the submission format\n", "    outputs = layers.Activation(\"linear\", name=\"outputs\")(out)\n", "    \n", "    inference_model = tf.keras.Model(inputs=inputs, outputs=outputs)\n", "    inference_model.compile(loss=\"sparse_categorical_crossentropy\",\n", "                            metrics=\"accuracy\")\n", "    return inference_model"]}, {"cell_type": "code", "execution_count": 20, "id": "ba7b3837", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:31:25.149667Z", "iopub.status.busy": "2023-04-22T14:31:25.149152Z", "iopub.status.idle": "2023-04-22T14:31:25.788409Z", "shell.execute_reply": "2023-04-22T14:31:25.787683Z"}, "papermill": {"duration": 1.740143, "end_time": "2023-04-22T14:31:25.880385", "exception": false, "start_time": "2023-04-22T14:31:24.140242", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"model_1\"\n", "__________________________________________________________________________________________________\n", " Layer (type)                   Output Shape         Param #     Connected to                     \n", "==================================================================================================\n", " inputs (InputLayer)            [(None, 543, 3)]     0           []                               \n", "                                                                                                  \n", " tf.compat.v1.gather (TFOpLambd  (None, 82, 3)       0           ['inputs[0][0]']                 \n", " a)                                                                                               \n", "                                                                                                  \n", " tf.math.is_nan (TFOpLambda)    (None, 82, 3)        0           ['tf.compat.v1.gather[0][0]']    \n", "                                                                                                  \n", " tf.zeros_like (TFOpLambda)     (None, 82, 3)        0           ['tf.compat.v1.gather[0][0]']    \n", "                                                                                                  \n", " tf.where (TFOpLambda)          (None, 82, 3)        0           ['tf.math.is_nan[0][0]',         \n", "                                                                  'tf.zeros_like[0][0]',          \n", "                                                                  'tf.compat.v1.gather[0][0]']    \n", "                                                                                                  \n", " tf.__operators__.getitem (Slic  (None, 82)          0           ['tf.where[0][0]']               \n", " ingOpLambda)                                                                                     \n", "                                                                                                  \n", " tf.__operators__.getitem_1 (Sl  (None, 82)          0           ['tf.where[0][0]']               \n", " icingOpLambda)                                                                                   \n", "                                                                                                  \n", " tf.__operators__.getitem_2 (Sl  (None, 82)          0           ['tf.where[0][0]']               \n", " icingOpLambda)                                                                                   \n", "                                                                                                  \n", " tf.concat (TFOpLambda)         (None, 246)          0           ['tf.__operators__.getitem[0][0]'\n", "                                                                 , 'tf.__operators__.getitem_1[0][\n", "                                                                 0]',                             \n", "                                                                  'tf.__operators__.getitem_2[0][0\n", "                                                                 ]']                              \n", "                                                                                                  \n", " tf.expand_dims (TFOpLambda)    (1, None, 246)       0           ['tf.concat[0][0]']              \n", "                                                                                                  \n", " model (Functional)             (None, 250)          719994      ['tf.expand_dims[0][0]']         \n", "|¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯|\n", "| input_1 (InputLayer)         [(None, None, 246)]  0           []                               |\n", "|                                                                                                |\n", "| dense (Dense)                (None, None, 256)    63232       []                               |\n", "|                                                                                                |\n", "| layer_normalization (LayerNorm  (None, None, 256)  512        []                               |\n", "| alization)                                                                                     |\n", "|                                                                                                |\n", "| activation (Activation)      (None, None, 256)    0           []                               |\n", "|                                                                                                |\n", "| dropout (Dropout)            (None, None, 256)    0           []                               |\n", "|                                                                                                |\n", "| dense_1 (<PERSON><PERSON>)              (None, None, 128)    32896       []                               |\n", "|                                                                                                |\n", "| layer_normalization_1 (<PERSON><PERSON><PERSON><PERSON>  (None, None, 128)  256        []                               |\n", "| rmalization)                                                                                   |\n", "|                                                                                                |\n", "| activation_1 (Activation)    (None, None, 128)    0           []                               |\n", "|                                                                                                |\n", "| dropout_1 (Dropout)          (None, None, 128)    0           []                               |\n", "|                                                                                                |\n", "| dense_2 (<PERSON><PERSON>)              (None, None, 256)    33024       []                               |\n", "|                                                                                                |\n", "| layer_normalization_2 (<PERSON><PERSON><PERSON><PERSON>  (None, None, 256)  512        []                               |\n", "| rmalization)                                                                                   |\n", "|                                                                                                |\n", "| activation_2 (Activation)    (None, None, 256)    0           []                               |\n", "|                                                                                                |\n", "| dropout_2 (Dropout)          (None, None, 256)    0           []                               |\n", "|                                                                                                |\n", "| lstm (LSTM)                  (None, 256)          525312      []                               |\n", "|                                                                                                |\n", "| outputs (Dense)              (None, 250)          64250       []                               |\n", "¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯\n", " outputs (Activation)           (1, 250)             0           ['model[0][0]']                  \n", "                                                                                                  \n", "==================================================================================================\n", "Total params: 719,994\n", "Trainable params: 719,994\n", "Non-trainable params: 0\n", "__________________________________________________________________________________________________\n"]}], "source": ["inference_model = get_inference_model(model)\n", "inference_model.summary(expand_nested=True)"]}, {"cell_type": "code", "execution_count": 21, "id": "a733ba2d", "metadata": {"_kg_hide-output": true, "execution": {"iopub.execute_input": "2023-04-22T14:31:27.497832Z", "iopub.status.busy": "2023-04-22T14:31:27.497469Z", "iopub.status.idle": "2023-04-22T14:31:41.304760Z", "shell.execute_reply": "2023-04-22T14:31:41.303702Z"}, "papermill": {"duration": 14.596927, "end_time": "2023-04-22T14:31:41.307537", "exception": false, "start_time": "2023-04-22T14:31:26.710610", "status": "completed"}, "tags": []}, "outputs": [], "source": ["if mode == \"training\":\n", "    converter = tf.lite.TFLiteConverter.from_keras_model(inference_model)\n", "    tflite_model = converter.convert()"]}, {"cell_type": "code", "execution_count": 22, "id": "5c8e8c1d", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:31:42.795376Z", "iopub.status.busy": "2023-04-22T14:31:42.794292Z", "iopub.status.idle": "2023-04-22T14:31:42.803374Z", "shell.execute_reply": "2023-04-22T14:31:42.802340Z"}, "papermill": {"duration": 0.783595, "end_time": "2023-04-22T14:31:42.805887", "exception": false, "start_time": "2023-04-22T14:31:42.022292", "status": "completed"}, "tags": []}, "outputs": [], "source": ["if mode == \"training\":\n", "    with open('/kaggle/working/model.tflite', 'wb') as f:\n", "        f.write(tflite_model)"]}, {"cell_type": "code", "execution_count": 23, "id": "c7bd0af5", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:31:44.304790Z", "iopub.status.busy": "2023-04-22T14:31:44.304424Z", "iopub.status.idle": "2023-04-22T14:31:44.309833Z", "shell.execute_reply": "2023-04-22T14:31:44.308736Z"}, "papermill": {"duration": 0.791868, "end_time": "2023-04-22T14:31:44.312336", "exception": false, "start_time": "2023-04-22T14:31:43.520468", "status": "completed"}, "tags": []}, "outputs": [], "source": ["if mode == \"submission\":\n", "    !cp -r /kaggle/input/tflite-model/model.tflite ./\n"]}, {"cell_type": "code", "execution_count": 24, "id": "27408b4d", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:31:45.742764Z", "iopub.status.busy": "2023-04-22T14:31:45.742219Z", "iopub.status.idle": "2023-04-22T14:31:47.047466Z", "shell.execute_reply": "2023-04-22T14:31:47.045677Z"}, "papermill": {"duration": 2.021731, "end_time": "2023-04-22T14:31:47.049932", "exception": false, "start_time": "2023-04-22T14:31:45.028201", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  adding: model.tflite (deflated 8%)\r\n"]}], "source": ["!zip submission.zip model.tflite"]}, {"cell_type": "markdown", "id": "428466cb", "metadata": {"papermill": {"duration": 0.716184, "end_time": "2023-04-22T14:31:48.550397", "exception": false, "start_time": "2023-04-22T14:31:47.834213", "status": "completed"}, "tags": []}, "source": ["# Submission code"]}, {"cell_type": "code", "execution_count": 25, "id": "c5901411", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:31:50.150903Z", "iopub.status.busy": "2023-04-22T14:31:50.150486Z", "iopub.status.idle": "2023-04-22T14:32:02.697772Z", "shell.execute_reply": "2023-04-22T14:32:02.696427Z"}, "papermill": {"duration": 13.357366, "end_time": "2023-04-22T14:32:02.700659", "exception": false, "start_time": "2023-04-22T14:31:49.343293", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting tflite-runtime==2.9.1\r\n", "  Downloading tflite_runtime-2.9.1-cp37-cp37m-manylinux2014_x86_64.whl (2.3 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m33.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hRequirement already satisfied: numpy>=1.19.2 in /opt/conda/lib/python3.7/site-packages (from tflite-runtime==2.9.1) (1.21.6)\r\n", "Installing collected packages: tflite-runtime\r\n", "Successfully installed tflite-runtime-2.9.1\r\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\r\n", "\u001b[0m"]}], "source": ["!pip install tflite-runtime==2.9.1\n", "import tflite_runtime.interpreter as tflite\n", "import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 26, "id": "79481ad0", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:32:04.201460Z", "iopub.status.busy": "2023-04-22T14:32:04.200351Z", "iopub.status.idle": "2023-04-22T14:32:04.207828Z", "shell.execute_reply": "2023-04-22T14:32:04.206642Z"}, "papermill": {"duration": 0.795711, "end_time": "2023-04-22T14:32:04.210171", "exception": false, "start_time": "2023-04-22T14:32:03.414460", "status": "completed"}, "tags": []}, "outputs": [], "source": ["ROWS_PER_FRAME = 543  # number of landmarks per frame\n", "\n", "def load_relevant_data_subset(pq_path):\n", "    data_columns = ['x', 'y', 'z']\n", "    data = pd.read_parquet(pq_path, columns=data_columns)\n", "    n_frames = int(len(data) / ROWS_PER_FRAME)\n", "    data = data.values.reshape(n_frames, ROWS_PER_FRAME, len(data_columns))\n", "    return data.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 27, "id": "0cb113ce", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:32:05.712875Z", "iopub.status.busy": "2023-04-22T14:32:05.712448Z", "iopub.status.idle": "2023-04-22T14:32:05.720661Z", "shell.execute_reply": "2023-04-22T14:32:05.719601Z"}, "papermill": {"duration": 0.795888, "end_time": "2023-04-22T14:32:05.722909", "exception": false, "start_time": "2023-04-22T14:32:04.927021", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# mode = \"debugging\"\n", "\n", "if mode == \"debugging\":    \n", "    import tflite_runtime.interpreter as tflite\n", "    \n", "    model_path = \"/kaggle/working/model.tflite\"\n", "#     model_path = \"/kaggle/input/tflite-model/model.tflite\"\n", "    \n", "    interpreter = tflite.Interpreter(model_path)\n", "    print(\"interpreter loaded\")\n", "\n", "    prediction_fn = interpreter.get_signature_runner(\"serving_default\")\n", "    precision_count = 0\n", "    for i in range(10):\n", "        X = load_relevant_data_subset(\"/kaggle/input/asl-signs/\"+file_paths[i])\n", "        output = prediction_fn(inputs=X)\n", "        sign = np.argmax(output[\"outputs\"])\n", "        print(\"Ground truth\", y_sign[i], sign_dict[y_sign[i]], \"___ Prediction\", ordered_signs[sign], sign)\n", "        if sign == sign_dict[y_sign[i]]:\n", "            precision_count += 1\n", "\n", "    print(precision_count)\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": 28, "id": "a67258a9", "metadata": {"execution": {"iopub.execute_input": "2023-04-22T14:32:07.219313Z", "iopub.status.busy": "2023-04-22T14:32:07.218888Z", "iopub.status.idle": "2023-04-22T14:32:07.237094Z", "shell.execute_reply": "2023-04-22T14:32:07.235120Z"}, "papermill": {"duration": 0.798075, "end_time": "2023-04-22T14:32:07.240363", "exception": false, "start_time": "2023-04-22T14:32:06.442288", "status": "completed"}, "tags": []}, "outputs": [], "source": ["if mode == \"debugging\":\n", "    model_path = \"/kaggle/working/model.tflite\"\n", "    tf.lite.experimental.Analyzer.analyze(model_path=model_path)        "]}, {"cell_type": "code", "execution_count": null, "id": "7bed9931", "metadata": {"papermill": {"duration": 0.827876, "end_time": "2023-04-22T14:32:08.823348", "exception": false, "start_time": "2023-04-22T14:32:07.995472", "status": "completed"}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.12"}, "papermill": {"default_parameters": {}, "duration": 1982.602905, "end_time": "2023-04-22T14:32:13.613893", "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2023-04-22T13:59:11.010988", "version": "2.4.0"}}, "nbformat": 4, "nbformat_minor": 5}